package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;

@ExtendWith(MockitoExtension.class)
class TransactionBuilderTest {

    @Mock private TaskExecutor taskExecutor;

    @Mock private DistributedAction<String> action1;

    @Mock private DistributedAction<Integer> action2;

    private TransactionOrchestrator orchestrator;
    private TransactionOrchestrator.TransactionBuilder builder;

    @BeforeEach
    void setUp() {
        // Initialize the orchestrator with mocked TaskExecutor
        orchestrator = new TransactionOrchestrator();
        // Inject TaskExecutor manually as we are not using Spring context
        try {
            var field = TransactionOrchestrator.class.getDeclaredField("taskExecutor");
            field.setAccessible(true);
            field.set(orchestrator, taskExecutor);
        } catch (Exception e) {
            fail("Failed to set taskExecutor field: " + e.getMessage());
        }

        // Create a new transaction builder
        builder = orchestrator.newTransaction();
    }

    @Test
    void addStep_shouldAddStepToList() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");

        // Act
        builder.addStep("step1", action1);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
    }

    @Test
    void addConditionalStep_whenConditionTrue_shouldExecuteStep() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Function<TransactionContext, Boolean> condition = context -> true;

        // Act
        builder.addConditionalStep("conditional_step", action1, condition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1).execute(any(TransactionContext.class));
    }

    @Test
    void addConditionalStep_whenConditionFalse_shouldSkipStep() throws Exception {
        // Arrange
        Function<TransactionContext, Boolean> condition = context -> false;

        // Act
        builder.addConditionalStep("skipped_step", action1, condition);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, never()).execute(any(TransactionContext.class));
    }

    @Test
    void execute_withMultipleSteps_shouldExecuteInOrder() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn(42);

        // Act
        builder.addStep("step1", action1);
        builder.addStep("step2", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
    }

    @Test
    void execute_whenStepFails_shouldReturnFailureResult() throws Exception {
        // Arrange
        Exception testException = new RuntimeException("Test exception");
        when(action1.execute(any())).thenThrow(testException);

        // Act
        builder.addStep("failing_step", action1);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
    }

    @Test
    void execute_whenStepFails_shouldTriggerCompensation() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Exception testException = new RuntimeException("Test exception");
        when(action2.execute(any())).thenThrow(testException);

        // Act
        builder.addStep("step1", action1);
        builder.addStep("failing_step", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1).compensate();
    }

    @Test
    void execute_withCompensationDisabled_shouldNotCompensate() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("result1");
        Exception testException = new RuntimeException("Test exception");
        when(action2.execute(any())).thenThrow(testException);

        // Act
        builder.withCompensation(false);
        builder.addStep("step1", action1);
        builder.addStep("failing_step", action2);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1, never()).compensate();
    }

    @Test
    void executeAsync_shouldExecuteInSeparateThread() throws Exception {
        // Arrange
        when(action1.execute(any())).thenReturn("async_result");

        // Setup taskExecutor to immediately run the provided runnable
        doAnswer(
                        invocation -> {
                            Runnable runnable = invocation.getArgument(0);
                            runnable.run();
                            return CompletableFuture.completedFuture(null);
                        })
                .when(taskExecutor)
                .execute(any(Runnable.class));

        // Act
        builder.addStep("async_step", action1);
        CompletableFuture<TransactionResult> future = builder.executeAsync();
        TransactionResult result = future.get(); // Block until completed

        // Assert
        assertTrue(result.success());
        verify(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    void executeStepWithRetry_shouldRetryFailedSteps() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(3, 10, 1.0, 100);
        Exception testException = new RuntimeException("Temporary failure");

        // Make action fail on first attempt, succeed on second attempt
        when(action1.execute(any())).thenThrow(testException).thenReturn("retry_success");

        // Create a custom step with retry policy
        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("retry_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithRetry);
        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action1, times(2))
                .execute(any(TransactionContext.class)); // Called twice due to retry
    }

    @Test
    void executeStepWithRetry_whenExceedsMaxAttempts_shouldFail() throws Exception {
        // Arrange
        RetryPolicy retryPolicy = new RetryPolicy(2, 10, 1.0, 100);
        Exception testException = new RuntimeException("Persistent failure");

        // Make action always fail
        when(action1.execute(any())).thenThrow(testException);

        // Create a custom step with retry policy
        TransactionStep<String> stepWithRetry =
                new TransactionStep.Builder<String>()
                        .name("failing_retry_step")
                        .action(action1)
                        .retryPolicy(retryPolicy)
                        .build();

        // Act
        builder.addStep(stepWithRetry);
        TransactionResult result = builder.execute();

        // Assert
        assertFalse(result.success());
        verify(action1, times(2))
                .execute(any(TransactionContext.class)); // Called twice due to retry
    }
}
